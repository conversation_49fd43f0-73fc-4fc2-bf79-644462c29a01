<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="glance_colorBackground">#ff1c1b1f</color>
    <color name="glance_colorError">#fff2b8b5</color>
    <color name="glance_colorErrorContainer">#ff8c1d18</color>
    <color name="glance_colorOnBackground">#ffe6e1e5</color>
    <color name="glance_colorOnError">#ff601410</color>
    <color name="glance_colorOnErrorContainer">#fff2b8b5</color>
    <color name="glance_colorOnPrimary">#ff381e72</color>
    <color name="glance_colorOnPrimaryContainer">#ffeaddff</color>
    <color name="glance_colorOnSecondary">#ff332d41</color>
    <color name="glance_colorOnSecondaryContainer">#ffe8def8</color>
    <color name="glance_colorOnSurface">#ffe6e1e5</color>
    <color name="glance_colorOnSurfaceInverse">#ff313033</color>
    <color name="glance_colorOnSurfaceVariant">#ffcac4d0</color>
    <color name="glance_colorOnTertiary">#ff492532</color>
    <color name="glance_colorOnTertiaryContainer">#ffffd8e4</color>
    <color name="glance_colorOutline">#ff938f99</color>
    <color name="glance_colorPrimary">#ffd0bcff</color>
    <color name="glance_colorPrimaryContainer">#ff4f378b</color>
    <color name="glance_colorPrimaryInverse">#ff6750a4</color>
    <color name="glance_colorSecondary">#ffccc2dc</color>
    <color name="glance_colorSecondaryContainer">#ff4a4458</color>
    <color name="glance_colorSurface">#ff1c1b1f</color>
    <color name="glance_colorSurfaceInverse">#ffe6e1e5</color>
    <color name="glance_colorSurfaceVariant">#ff49454f</color>
    <color name="glance_colorTertiary">#ffefb8c8</color>
    <color name="glance_colorTertiaryContainer">#ff633b48</color>
    <color name="glance_colorWidgetBackground">#ff20333d</color>
    <style name="Theme.AppCompat.DayNight" parent="Theme.AppCompat"/>
    <style name="Theme.AppCompat.DayNight.DarkActionBar" parent="Theme.AppCompat"/>
    <style name="Theme.AppCompat.DayNight.Dialog" parent="Theme.AppCompat.Dialog"/>
    <style name="Theme.AppCompat.DayNight.Dialog.Alert" parent="Theme.AppCompat.Dialog.Alert"/>
    <style name="Theme.AppCompat.DayNight.Dialog.MinWidth" parent="Theme.AppCompat.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.DayNight.DialogWhenLarge" parent="Theme.AppCompat.DialogWhenLarge"/>
    <style name="Theme.AppCompat.DayNight.NoActionBar" parent="Theme.AppCompat.NoActionBar"/>
    <style name="ThemeOverlay.AppCompat.DayNight" parent="ThemeOverlay.AppCompat.Dark"/>
</resources>