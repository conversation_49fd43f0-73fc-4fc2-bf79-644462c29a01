<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="glance_colorBackground">@android:color/system_neutral1_900</color>
    <color name="glance_colorError">#fff2b8b5</color>
    <color name="glance_colorErrorContainer">#ff8c1d18</color>
    <color name="glance_colorOnBackground">@android:color/system_neutral1_100</color>
    <color name="glance_colorOnError">#ff601410</color>
    <color name="glance_colorOnErrorContainer">#fff2b8b5</color>
    <color name="glance_colorOnPrimary">@android:color/system_accent1_800</color>
    <color name="glance_colorOnPrimaryContainer">@android:color/system_accent1_100</color>
    <color name="glance_colorOnSecondary">@android:color/system_accent2_800</color>
    <color name="glance_colorOnSecondaryContainer">@android:color/system_accent2_100</color>
    <color name="glance_colorOnSurface">@android:color/system_neutral1_100</color>
    <color name="glance_colorOnSurfaceInverse">@android:color/system_neutral1_800</color>
    <color name="glance_colorOnSurfaceVariant">@android:color/system_neutral2_200</color>
    <color name="glance_colorOnTertiary">@android:color/system_accent3_800</color>
    <color name="glance_colorOnTertiaryContainer">@android:color/system_accent3_100</color>
    <color name="glance_colorOutline">@android:color/system_neutral2_400</color>
    <color name="glance_colorPrimary">@android:color/system_accent1_200</color>
    <color name="glance_colorPrimaryContainer">@android:color/system_accent1_700</color>
    <color name="glance_colorPrimaryInverse">@android:color/system_accent1_600</color>
    <color name="glance_colorSecondary">@android:color/system_accent2_200</color>
    <color name="glance_colorSecondaryContainer">@android:color/system_accent2_700</color>
    <color name="glance_colorSurface">@android:color/system_neutral1_900</color>
    <color name="glance_colorSurfaceInverse">@android:color/system_neutral1_100</color>
    <color name="glance_colorSurfaceVariant">@android:color/system_neutral2_700</color>
    <color name="glance_colorTertiary">@android:color/system_accent3_200</color>
    <color name="glance_colorTertiaryContainer">@android:color/system_accent3_700</color>
    <color name="glance_colorWidgetBackground">@android:color/system_accent2_800</color>
</resources>