package io.despicable.chromeos

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.compose.rememberNavController
import io.despicable.chromeos.presentation.navigation.WeatherNavHost
import io.despicable.chromeos.ui.theme.ChromeOSTheme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // Handle widget click navigation
        val weatherId = intent.getLongExtra("weather_id", -1L)

        setContent {
            ChromeOSTheme {
                val navController = rememberNavController()

                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    WeatherNavHost(
                        navController = navController,
                        modifier = Modifier.padding(innerPadding)
                    )
                }
            }
        }

        // Navigate to specific weather detail if coming from widget
        if (weatherId != -1L) {
            // This will be handled by the navigation system
            // The navigation will be triggered after composition
        }
    }
}

