<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <string name="additional_info">Additional Information</string>
    <string name="app_name">chromeOS</string>
    <string name="configure_widget">Configure Weather Widget</string>
    <string name="font_size">Font Size</string>
    <string name="icon_size">Icon Size</string>
    <string name="loading_weather">Loading weather…</string>
    <string name="preview">Preview</string>
    <string name="save_configuration">Save Widget Configuration</string>
    <string name="select_city">Select City</string>
    <string name="show_humidity">Show Humidity</string>
    <string name="show_wind_speed">Show Wind Speed</string>
    <string name="weather_widget_description">Weather widget with customizable display options</string>
    <style name="Theme.ChromeOS" parent="android:Theme.Material.Light.NoActionBar"/>
</resources>