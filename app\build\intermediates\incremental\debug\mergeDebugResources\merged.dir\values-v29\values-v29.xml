<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Glance.AppWidget.TextAppearance.Bold" parent="Glance.AppWidget.TextAppearance.DeviceDefaultFontFamily">
        <item name="android:textFontWeight">700</item>
    </style>
    <style name="Glance.AppWidget.TextAppearance.DeviceDefaultFontFamily" parent="@android:style/TextAppearance.DeviceDefault">
        <item name="android:elegantTextHeight">@null</item>
        <item name="android:fontFeatureSettings">@null</item>
        <item name="android:fontVariationSettings">@null</item>
        <item name="android:letterSpacing">@null</item>
        <item name="android:shadowColor">@null</item>
        <item name="android:shadowDx">@null</item>
        <item name="android:shadowDy">@null</item>
        <item name="android:shadowRadius">@null</item>
        <item name="android:textColor">@null</item>
        <item name="android:textColorLink">@null</item>
        <item name="android:textSize">@null</item>
        <item name="android:typeface">@null</item>
    </style>
    <style name="Glance.AppWidget.TextAppearance.Medium" parent="Glance.AppWidget.TextAppearance.DeviceDefaultFontFamily">
        <item name="android:textFontWeight">500</item>
    </style>
    <style name="Glance.AppWidget.TextAppearance.Normal" parent="Glance.AppWidget.TextAppearance.DeviceDefaultFontFamily">
        <item name="android:textFontWeight">400</item>
    </style>
    <style name="Glance.AppWidget.Theme" parent="android:Theme.DeviceDefault.DayNight"/>
    <style name="Glance.AppWidget.Theme.GridChildren" parent=""/>
    <style name="Glance.AppWidget.Theme.ListChildren" parent=""/>
</resources>